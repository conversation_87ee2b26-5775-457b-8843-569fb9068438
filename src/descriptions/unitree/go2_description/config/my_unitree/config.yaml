# 模型文件信息
model_name: policy.pt
framework: isaacgym     # 或 isaacsim，根据训练时使用的框架决定

# 策略结构参数
num_of_dofs: 12         # 动作维度，通常为 12（4条腿 × 3关节）
num_observations: 141   # 观测维度（frame_stack × num_single_obs）

# 观测项配置（顺序需与训练时一致）
observations:
  - sin_time
  - cos_time
  - lin_vel_cmd_x
  - lin_vel_cmd_y
  - ang_vel_cmd_yaw
  - ang_vel
  - base_orientation
  - dof_pos
  - dof_vel
  - previous_actions

# 观测历史堆叠（帧堆叠）
observations_history: [0, 1, 2]    # 3帧堆叠，帧索引顺序从旧到新
frame_stack: 3                     # 显式标明帧堆叠数
decimation: 4                      # 控制器频率 / 策略频率

# 限幅与归一化配置
clip_obs: 100.0                    # 观测限幅
clip_actions_lower: [-1.0] * 12    # 动作下限
clip_actions_upper: [1.0] * 12     # 动作上限

# 动作缩放参数
action_scale: 0.25                 # 对推理结果缩放
hip_scale_reduction: 0.5          # hip关节动作缩放因子（避免大幅摆动）
hip_scale_reduction_indices: [0, 3, 6, 9]

# 控制器 PD 增益（单位为 N*m/rad）
rl_kp: [20.0] * 12
rl_kd: [0.5] * 12

# 力矩限幅（若使用力矩模式）
torque_limits: [45.0] * 12

# 默认关节初始位置（常用于动作偏移）
default_dof_pos: [
  0.0, 0.8, -1.5,
 -0.0, 0.8, -1.5,
  0.0, 0.8, -1.5,
 -0.0, 0.8, -1.5
]

# 观测缩放系数（用于归一化）
lin_vel_scale: 2.0
ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05

# 命令缩放（默认使用 lin_vel_scale / ang_vel_scale 组装）
commands_scale: [2.0, 2.0, 0.25]
